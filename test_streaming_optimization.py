#!/usr/bin/env python3
"""
测试流式输出优化效果的脚本
"""
import asyncio
import json
import websockets
import time

async def test_streaming_response():
    """测试流式响应的延迟和连续性"""
    
    # 连接到WebSocket
    uri = "ws://localhost:8000/ws/chat/test_chat_id?user_id=test_user"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送测试消息
            test_message = {
                "content": "请给我讲一个简短的故事"
            }
            
            print(f"📤 发送消息: {test_message['content']}")
            await websocket.send(json.dumps(test_message))
            
            # 记录时间戳
            start_time = time.time()
            first_chunk_time = None
            chunk_count = 0
            total_content = ""
            
            print("\n🔄 开始接收流式响应:")
            print("-" * 50)
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    current_time = time.time()
                    
                    if data.get('type') == 'message_chunk':
                        chunk_count += 1
                        chunk_content = data.get('content_chunk', '')
                        total_content += chunk_content
                        
                        if first_chunk_time is None:
                            first_chunk_time = current_time
                            delay = (first_chunk_time - start_time) * 1000
                            print(f"⚡ 首个chunk延迟: {delay:.2f}ms")
                        
                        # 显示chunk内容（截断长内容）
                        display_content = chunk_content.replace('\n', '\\n')
                        if len(display_content) > 30:
                            display_content = display_content[:30] + "..."
                        
                        elapsed = (current_time - start_time) * 1000
                        print(f"📦 Chunk {chunk_count:2d} ({elapsed:6.1f}ms): '{display_content}'")
                        
                    elif data.get('type') == 'message_final':
                        final_time = current_time
                        total_delay = (final_time - start_time) * 1000
                        
                        print("-" * 50)
                        print(f"✅ 流式响应完成!")
                        print(f"📊 统计信息:")
                        print(f"   - 总延迟: {total_delay:.2f}ms")
                        print(f"   - 首chunk延迟: {(first_chunk_time - start_time) * 1000:.2f}ms")
                        print(f"   - Chunk数量: {chunk_count}")
                        print(f"   - 总字符数: {len(total_content)}")
                        print(f"   - 平均chunk大小: {len(total_content)/chunk_count:.1f}字符")
                        
                        if chunk_count > 0:
                            avg_chunk_interval = (final_time - first_chunk_time) * 1000 / (chunk_count - 1) if chunk_count > 1 else 0
                            print(f"   - 平均chunk间隔: {avg_chunk_interval:.2f}ms")
                        
                        break
                        
                    elif data.get('type') == 'error':
                        print(f"❌ 错误: {data.get('content', 'Unknown error')}")
                        break
                        
                except json.JSONDecodeError:
                    print(f"⚠️  无法解析消息: {message}")
                except Exception as e:
                    print(f"⚠️  处理消息时出错: {e}")
                    
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket连接已关闭")
    except Exception as e:
        print(f"❌ 连接错误: {e}")

def print_optimization_summary():
    """打印优化总结"""
    print("\n" + "="*60)
    print("🚀 流式输出优化总结")
    print("="*60)
    print("✅ 后端优化:")
    print("   - 移除了复杂的<reply>标签解析缓冲机制")
    print("   - LLM生成的每个chunk立即通过WebSocket推送")
    print("   - 减少了首字符显示延迟")
    print()
    print("✅ 前端优化:")
    print("   - 优化了打字机组件的计时器管理")
    print("   - 增量更新时保持动画连续性")
    print("   - 避免了不必要的计时器重置")
    print()
    print("🎯 预期效果:")
    print("   - 用户发送消息后几乎立即看到NPC开始'输入'")
    print("   - 打字机动画与LLM生成速度同步")
    print("   - 更流畅、自然的视觉反馈")
    print("="*60)

if __name__ == "__main__":
    print_optimization_summary()
    print("\n🧪 开始测试流式输出优化效果...")
    print("请确保后端服务正在运行 (python -m uvicorn backend.src.supabase_main:app --reload)")
    
    try:
        asyncio.run(test_streaming_response())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")