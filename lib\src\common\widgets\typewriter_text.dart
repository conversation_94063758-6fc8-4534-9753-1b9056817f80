import 'dart:async';
import 'package:flutter/material.dart';

class TypewriterText extends StatefulWidget {
  final String text;
  final TextStyle style;
  final Duration duration;
  final bool skipAnimation; // 是否跳过动画直接显示
  final bool forceComplete; // 新增：是否强制立即完成显示

  const TypewriterText(
    this.text, {
    super.key,
    required this.style,
    this.duration = const Duration(milliseconds: 50),
    this.skipAnimation = false, // 默认不跳过
    this.forceComplete = false, // 默认不强制完成
  });

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText> {
  String _displayedText = "";
  Timer? _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    if (widget.skipAnimation || widget.forceComplete) {
      // 如果跳过动画或强制完成，直接显示全部文本
      _displayedText = widget.text;
      _currentIndex = widget.text.length;
    } else {
      _startTyping();
    }
  }

  @override
  void didUpdateWidget(TypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果强制完成状态发生变化，立即完成显示
    if (widget.forceComplete && !oldWidget.forceComplete) {
      _timer?.cancel();
      setState(() {
        _displayedText = widget.text;
        _currentIndex = widget.text.length;
      });
      return;
    }

    if (widget.text != oldWidget.text) {
      if (widget.skipAnimation || widget.forceComplete) {
        // 如果跳过动画或强制完成，直接更新显示文本
        setState(() {
          _displayedText = widget.text;
          _currentIndex = widget.text.length;
        });
      } else {
        // 检查是否是增量更新（新文本是旧文本的扩展）
        if (widget.text.startsWith(_displayedText) && widget.text.length > _displayedText.length) {
          // 如果是增量更新，只需确保计时器正在运行即可
          _startOrContinueTyping();
        } else {
          // 如果是全新的文本，则重置并重新开始
          _resetAndStart();
        }
      }
    }
  }

  void _startTyping() {
    _timer?.cancel();
    _currentIndex = 0;
    _displayedText = "";
    _startOrContinueTyping();
  }
  
  void _startOrContinueTyping() {
    // 仅当计时器未激活时才创建新的计时器
    if (_timer?.isActive ?? false) {
      return;
    }
    
    _timer = Timer.periodic(widget.duration, (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      // 计时器会自然地使用 widget.text 的最新长度
      if (_currentIndex < widget.text.length) {
        setState(() {
          _currentIndex++;
          _displayedText = widget.text.substring(0, _currentIndex);
        });
      } else {
        timer.cancel();
      }
    });
  }
  
  void _resetAndStart() {
    if(mounted) {
      setState(() {
        _currentIndex = 0;
        _displayedText = "";
      });
    }
    _startTyping();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _displayedText,
      style: widget.style,
    );
  }
}
