// lib/src/features/chat/bloc/chat_player/chat_player_state.dart

part of 'chat_player_bloc.dart';

enum ChatPlayerStatus { initial, loading, success, failure }

// PRD要求：清晰的故事交互状态机
enum StoryInteractionState { 
  cinematic, // 电影式演绎模式
  gameplay,  // 游戏模式（显示任务、可输入）
  finished   // 章节完成
}

// 玩家交互状态（从StoryPlayerBloc迁移）
enum PlayerInteractionState {
  playing,   // 正在游戏中，可以进行交互
  loading,   // 加载中，等待服务器响应
  finished,  // 章节或故事完成
}

class ChatPlayerState extends Equatable {
  final ChatPlayerStatus status;
  final String? chatId;
  final String? userId; // 新增：用户ID
  final List<Message> messages;
  final List<Agent> participants;
  final String? errorMessage;
  final Agent? currentTargetAgent;
  
  // --- PRD重构后的故事模式字段 ---
  final story_models.InteractiveStoryDetail? storyDetail;
  final Agent? protagonistAgent; // 玩家扮演的女主角Agent
  final StoryInteractionState storyInteractionState; // 使用新枚举
  final String? missionText;
  final int currentProgress;
  final int maxProgress;
  final Map<String, dynamic>? taskProgress; // 支持PRD的新JSON结构

  final bool isReplying;
  final List<Choice>? currentChoices;
  final bool isGeneratingChoices; // 新增：追踪选项是否正在生成中
  final bool areChoicesCollapsed; // 新增：追踪选项框是否收缩
  final bool showChoicesPanel; // 新增：控制选项面板是否可见
  final bool justMadeChoice; // 标记用户是否刚刚做出选择
  final bool isWaitingForNextMessage; // 防连点保护：是否正在等待下一条消息

  // --- 章节介绍和渐进式故事模式字段 ---
  final bool showChapterIntro;
  final List<Map<String, dynamic>>? chapterElements;
  final bool isProgressiveStoryMode;
  final int currentStoryElementIndex;
  final bool isLoadingNextElement;

  // --- 其他故事模式状态字段 ---
  final PlayerInteractionState interactionState;
  final List<story_models.StoryNode> displayedNodes;
  final story_models.StoryNode? currentNode;
  final int score;
  final bool isChatSceneReady;
  final List<Agent> agentsInScene;
  final bool hasMoreMessages; // 是否还有更多历史消息可加载

  const ChatPlayerState({
    this.status = ChatPlayerStatus.initial,
    this.chatId,
    this.userId, // 新增：用户ID
    this.messages = const <Message>[],
    this.participants = const <Agent>[],
    this.errorMessage,
    this.currentTargetAgent,
    this.storyDetail,
    this.protagonistAgent,
    this.storyInteractionState = StoryInteractionState.cinematic, // 默认是演绎模式
    this.missionText,
    this.currentProgress = 0,
    this.maxProgress = 100,
    this.taskProgress,
    this.isReplying = false,
    this.currentChoices,
    this.isGeneratingChoices = false, // 新增：默认值为 false
    this.areChoicesCollapsed = false, // 新增：默认为展开状态 (false)
    this.showChoicesPanel = false, // 新增：默认不显示选项面板
    this.justMadeChoice = false,
    this.isWaitingForNextMessage = false,
    // 章节介绍和渐进式故事模式字段
    this.showChapterIntro = false,
    this.chapterElements,
    this.isProgressiveStoryMode = false,
    this.currentStoryElementIndex = -1,
    this.isLoadingNextElement = false,
    // 其他故事模式状态字段
    this.interactionState = PlayerInteractionState.playing,
    this.displayedNodes = const <story_models.StoryNode>[],
    this.currentNode,
    this.score = 0,
    this.isChatSceneReady = false,
    this.agentsInScene = const <Agent>[],
    this.hasMoreMessages = true, // 默认假设还有更多消息
  });

  ChatPlayerState copyWith({
    ChatPlayerStatus? status,
    String? chatId,
    String? userId, // 新增：用户ID
    List<Message>? messages,
    List<Agent>? participants,
    String? errorMessage,
    Agent? currentTargetAgent,
    story_models.InteractiveStoryDetail? storyDetail,
    Agent? protagonistAgent,
    StoryInteractionState? storyInteractionState,
    String? missionText,
    int? currentProgress,
    int? maxProgress,
    Map<String, dynamic>? taskProgress,
    bool? isReplying,
    List<Choice>? currentChoices,
    bool? isGeneratingChoices, // 新增
    bool? areChoicesCollapsed, // 新增
    bool? showChoicesPanel, // 新增
    bool? justMadeChoice,
    bool? isWaitingForNextMessage,
    // 章节介绍和渐进式故事模式字段
    bool? showChapterIntro,
    List<Map<String, dynamic>>? chapterElements,
    bool? isProgressiveStoryMode,
    int? currentStoryElementIndex,
    bool? isLoadingNextElement,
    // 其他故事模式状态字段
    PlayerInteractionState? interactionState,
    List<story_models.StoryNode>? displayedNodes,
    story_models.StoryNode? currentNode,
    int? score,
    bool? isChatSceneReady,
    List<Agent>? agentsInScene,
    bool? hasMoreMessages,
  }) {
    return ChatPlayerState(
      status: status ?? this.status,
      chatId: chatId ?? this.chatId,
      userId: userId ?? this.userId, // 新增：用户ID
      messages: messages ?? this.messages,
      participants: participants ?? this.participants,
      errorMessage: errorMessage ?? this.errorMessage,
      currentTargetAgent: currentTargetAgent ?? this.currentTargetAgent,
      storyDetail: storyDetail ?? this.storyDetail,
      protagonistAgent: protagonistAgent ?? this.protagonistAgent,
      storyInteractionState: storyInteractionState ?? this.storyInteractionState,
      missionText: missionText ?? this.missionText,
      currentProgress: currentProgress ?? this.currentProgress,
      maxProgress: maxProgress ?? this.maxProgress,
      taskProgress: taskProgress ?? this.taskProgress,
      isReplying: isReplying ?? this.isReplying,
      currentChoices: currentChoices ?? this.currentChoices,
      isGeneratingChoices: isGeneratingChoices ?? this.isGeneratingChoices, // 新增
      areChoicesCollapsed: areChoicesCollapsed ?? this.areChoicesCollapsed, // 新增
      showChoicesPanel: showChoicesPanel ?? this.showChoicesPanel, // 新增
      justMadeChoice: justMadeChoice ?? this.justMadeChoice,
      isWaitingForNextMessage: isWaitingForNextMessage ?? this.isWaitingForNextMessage,
      // 章节介绍和渐进式故事模式字段
      showChapterIntro: showChapterIntro ?? this.showChapterIntro,
      chapterElements: chapterElements ?? this.chapterElements,
      isProgressiveStoryMode: isProgressiveStoryMode ?? this.isProgressiveStoryMode,
      currentStoryElementIndex: currentStoryElementIndex ?? this.currentStoryElementIndex,
      isLoadingNextElement: isLoadingNextElement ?? this.isLoadingNextElement,
      // 其他故事模式状态字段
      interactionState: interactionState ?? this.interactionState,
      displayedNodes: displayedNodes ?? this.displayedNodes,
      currentNode: currentNode ?? this.currentNode,
      score: score ?? this.score,
      isChatSceneReady: isChatSceneReady ?? this.isChatSceneReady,
      agentsInScene: agentsInScene ?? this.agentsInScene,
      hasMoreMessages: hasMoreMessages ?? this.hasMoreMessages,
    );
  }

  @override
  List<Object?> get props => [
    status, chatId, userId, messages, participants, errorMessage, currentTargetAgent,
    storyDetail, protagonistAgent, storyInteractionState, missionText, currentProgress, maxProgress,
    taskProgress, isReplying, currentChoices, isGeneratingChoices, areChoicesCollapsed, showChoicesPanel, justMadeChoice, isWaitingForNextMessage,
    // 章节介绍和渐进式故事模式字段
    showChapterIntro, chapterElements, isProgressiveStoryMode, currentStoryElementIndex, isLoadingNextElement,
    // 其他故事模式状态字段
    interactionState, displayedNodes, currentNode, score, isChatSceneReady, agentsInScene, hasMoreMessages,
  ];
}
