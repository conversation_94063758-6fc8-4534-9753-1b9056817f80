// lib/src/features/chat/presentation/widgets/choice_buttons.dart

import 'dart:ui'; // 引入 dart:ui 以使用 ImageFilter
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/features/chat/models/choice_model.dart';
import 'package:xinglian/src/features/chat/models/message_model.dart';

/// 一个显示在聊天界面内部的选项按钮列表组件
/// 替代原有的弹窗式选择，直接内联显示在聊天记录和输入框之间
/// 支持展开/收缩状态和重新生成功能
class ChoiceButtons extends StatelessWidget {
  const ChoiceButtons({super.key});

  @override
  Widget build(BuildContext context) {
    // 使用 BlocBuilder 来监听展开/收缩状态
    return BlocBuilder<ChatPlayerBloc, ChatPlayerState>(
      buildWhen: (prev, current) =>
          prev.areChoicesCollapsed != current.areChoicesCollapsed ||
          prev.currentChoices != current.currentChoices,
      builder: (context, state) {
        final choices = state.currentChoices ?? [];

        // 根据状态决定显示哪个视图
        if (state.areChoicesCollapsed) {
          return _buildCollapsedView(context);
        } else {
          return _buildExpandedChoices(context, choices);
        }
      },
    );
  }

  // 构建展开后的选项框
  Widget _buildExpandedChoices(BuildContext context, List<Choice> choices) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: AppColors.secondaryBg.withOpacity(0.8),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: AppColors.secondaryText.withOpacity(0.2)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 新增的顶部控制栏
          _buildHeader(context),
          const SizedBox(height: 8),
          // 原始的选项按钮列表
          ...choices.map((choice) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: ElevatedButton(
                onPressed: () => _onChoiceSelected(context, choice),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondaryBg.withOpacity(0.6),
                  foregroundColor: AppColors.primaryText,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.0),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  choice.text,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryText,
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  // 新增：构建顶部控制栏
  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '请选择：',
          style: TextStyle(color: AppColors.secondaryText, fontSize: 14),
        ),
        Row(
          children: [
            // 重新生成按钮
            IconButton(
              icon: const Icon(Icons.refresh, color: AppColors.secondaryText, size: 20),
              onPressed: () {
                context.read<ChatPlayerBloc>().add(RegenerateChoicesRequested());
              },
              tooltip: '重新生成选项',
            ),
            // 收缩按钮
            IconButton(
              icon: const Icon(Icons.unfold_less, color: AppColors.secondaryText, size: 20),
              onPressed: () {
                context.read<ChatPlayerBloc>().add(ToggleChoicesExpansion());
              },
              tooltip: '收起选项',
            ),
          ],
        ),
      ],
    );
  }

  // 新增：构建收缩后的视图
  Widget _buildCollapsedView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
      child: Center(
        child: InkWell(
          onTap: () {
            context.read<ChatPlayerBloc>().add(ToggleChoicesExpansion());
          },
          borderRadius: BorderRadius.circular(20),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.auto_awesome, color: AppColors.secondaryText, size: 16),
                    SizedBox(width: 8),
                    Text(
                      '自动回复',
                      style: TextStyle(color: AppColors.secondaryText, fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onChoiceSelected(BuildContext context, Choice choice) {
    final bloc = context.read<ChatPlayerBloc>();
    final state = bloc.state;

    // 优先使用选项的targetAgentIndex，然后是targetAgentId
    String? targetAgentId = choice.targetAgentId;

    // 如果有targetAgentIndex，根据序号查找对应的角色ID
    if (choice.targetAgentIndex != null && choice.targetAgentIndex! >= 0) {
      final index = choice.targetAgentIndex!; // 使用0-based索引
      if (index < state.participants.length) {
        targetAgentId = state.participants[index].id;
        print('Choice target resolved: index ${choice.targetAgentIndex} -> agent ${state.participants[index].name} (${targetAgentId})');
      } else {
        print('Warning: targetAgentIndex ${choice.targetAgentIndex} out of range, participants count: ${state.participants.length}');
      }
    }

    // 如果选项没有指定目标，则回退到旧的"最后一个发言者"逻辑
    if (targetAgentId == null) {
      // 优先逻辑：从后往前遍历消息历史，找到最后一个发言的NPC
      for (int i = state.messages.length - 1; i >= 0; i--) {
        final message = state.messages[i];
        if (message.role == MessageRole.assistant && message.agentId != null) {
          targetAgentId = message.agentId;
          break;
        }
      }

      // 备用逻辑：如果历史记录中没有NPC发言
      if (targetAgentId == null) {
        try {
          // 找到第一个不是主角的NPC
          final npcAgent = state.participants.firstWhere(
            (participant) => participant.id != state.protagonistAgent?.id,
          );
          targetAgentId = npcAgent.id;
        } catch (e) {
          // 最终保险：选择第一个参与者
          if (state.participants.isNotEmpty) {
            targetAgentId = state.participants.first.id;
          }
        }
      }
    }

    // 发送带有选择内容的消息事件
    bloc.add(SendMessage(
      content: choice.text,
      targetAgentId: targetAgentId, // <--- 使用最终确定的ID
      // choiceEffect已移除，统一使用评分模型
    ));
  }
}
