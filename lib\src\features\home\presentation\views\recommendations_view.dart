// lib/src/features/home/<USER>/views/recommendations_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/auth/bloc/auth_bloc.dart';
import 'package:xinglian/src/features/chat/repository/chat_repository.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_bloc.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_models.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_repository.dart';
import 'package:xinglian/src/features/interactive_story/models/interactive_story_model.dart';
import 'package:go_router/go_router.dart';

class RecommendationsView extends StatelessWidget {
  const RecommendationsView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RecommendationBloc(
        RepositoryProvider.of<RecommendationRepository>(context),
      )..add(LoadInitialRecommendations()),
      child: BlocBuilder<RecommendationBloc, RecommendationState>(
        builder: (context, state) {
           if (state is RecommendationLoading || state is RecommendationInitial) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is RecommendationLoaded) {
            // 使用 CustomScrollView 实现统一滚动和吸顶效果
            return CustomScrollView(
              slivers: [
                // 热门榜部分，作为Sliver列表的一部分
                SliverToBoxAdapter(
                  child: _RankingSection(state: state),
                ),
                // 筛选标签栏，实现吸顶效果
                SliverPersistentHeader(
                  delegate: _SliverFilterTagBarDelegate(
                    child: _FilterTagBar(state: state),
                  ),
                  pinned: true,
                ),
                // 内容瀑布流
                _ContentFeed(
                  items: state.feedItems,
                  isLoading: state.isFeedLoading,
                ),
              ],
            );
          }
          if (state is RecommendationError) {
            return Center(child: Text(state.message, style: const TextStyle(color: Colors.white)));
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}

// -- Widgets for each section --

class _RankingSection extends StatelessWidget {
  final RecommendationLoaded state;
  const _RankingSection({required this.state});

  void _onTabTapped(BuildContext context, RankingType type) {
    if (state.currentRankingType != type) {
       context.read<RecommendationBloc>().add(ChangeRankingType(type));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('热门榜', style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                    color: AppColors.inputBackground,
                    borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    _buildRankingTab(context, '互动剧情', RankingType.story),
                    _buildRankingTab(context, '人气角色', RankingType.agent),
                  ],
                ),
              )
            ],
          ),
          const SizedBox(height: 16),
          // !!! 核心修改：使用横向滚动大卡片 !!!
          SizedBox(
            height: 220, // 卡片高度
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: state.rankingItems.length,
              itemBuilder: (context, index) {
                return _RankingCard(
                  item: state.rankingItems[index],
                  rankingType: state.currentRankingType,
                );
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _buildRankingTab(BuildContext context, String title, RankingType type) {
    final bool isActive = state.currentRankingType == type;
    return GestureDetector(
        onTap: () => _onTabTapped(context, type),
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
                color: isActive ? AppColors.secondaryBg : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
            ),
            child: Text(title, style: TextStyle(color: isActive ? Colors.white : AppColors.secondaryText, fontSize: 14, fontWeight: FontWeight.w500)),
        ),
    );
  }
}

class _RankingCard extends StatelessWidget {
    final RankingItem item;
    final RankingType rankingType;
    const _RankingCard({required this.item, required this.rankingType});

    @override
    Widget build(BuildContext context) {
        return GestureDetector(
            onTap: () async {
              final authState = context.read<AuthBloc>().state;
              String? userId;
              if (authState is Authenticated) {
                userId = authState.userId;
              }
              if (userId == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('请先登录')),
                );
                return;
              }

              // 根据rankingType决定跳转逻辑
              String? chatId;
              if (rankingType == RankingType.agent) {
                // 角色卡：启动角色聊天
                chatId = await context.read<ChatRepository>().startAgentChat(item.id, userId);
              } else {
                // 剧情卡：启动剧情聊天
                chatId = await context.read<ChatRepository>().startStory(item.id, userId);
              }

              if (chatId != null && context.mounted) {
                context.push('/chat/$chatId');
              } else {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('启动聊天失败，请重试')),
                  );
                }
              }
            },
            child: Container(
                width: 150,
                margin: const EdgeInsets.only(right: 12),
                child: Stack(
                    fit: StackFit.expand,
                    children: [
                        ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: item.coverUrl.isNotEmpty
                                ? Image.network(
                                    item.coverUrl,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) => Container(
                                      color: AppColors.inputBackground,
                                      child: const Icon(Icons.menu_book, size: 64, color: AppColors.secondaryText),
                                    ),
                                  )
                                : Container(
                                    color: AppColors.inputBackground,
                                    child: const Icon(Icons.menu_book, size: 64, color: AppColors.secondaryText),
                                  ),
                        ),
                        // Gradient overlay for text readability
                        Container(
                             decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                gradient: LinearGradient(
                                    colors: [Colors.transparent, Colors.black.withOpacity(0.8)],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter
                                )
                             )
                        ),
                        Positioned(
                            bottom: 12,
                            left: 12,
                            right: 12,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                    Text(item.title, style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold), maxLines: 2, overflow: TextOverflow.ellipsis),
                                    const SizedBox(height: 4),
                                    Row(children: [
                                        const Icon(Icons.chat_bubble_outline, color: Colors.white, size: 12),
                                        const SizedBox(width: 4),
                                        Text(item.popularity, style: const TextStyle(color: Colors.white, fontSize: 12)),
                                    ],)
                                ],
                            )
                        )
                    ],
                ),
            ),
        );
    }
}


class _FilterTagBar extends StatelessWidget {
  final RecommendationLoaded state;
  const _FilterTagBar({required this.state});

  void _onTagTapped(BuildContext context, String tag) {
    if (state.currentTag != tag) {
       context.read<RecommendationBloc>().add(FilterByTag(tag));
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<String> tags = ['推荐', '恋爱', '剧情', '互动剧情', '男频', '陪伴', '幻想'];
    
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColors.background,
        border: Border(bottom: BorderSide(color: AppColors.inputBackground))
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: tags.length,
        itemBuilder: (context, index) {
          final tag = tags[index];
          final isActive = state.currentTag == tag;
          return GestureDetector(
            onTap: () => _onTagTapped(context, tag),
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: isActive ? AppColors.accentPurple : AppColors.inputBackground,
                borderRadius: BorderRadius.circular(18),
              ),
              child: Text(tag, style: TextStyle(color: isActive ? Colors.white : AppColors.secondaryText, fontWeight: FontWeight.w500)),
            ),
          );
        },
      ),
    );
  }
}

class _ContentFeed extends StatelessWidget {
  final List<RecommendationItem> items;
  final bool isLoading;
  const _ContentFeed({required this.items, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    // 将瀑布流改为 Sliver 版本
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverMasonryGrid.count(
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          if (item.type == RecommendationItemType.agent) {
            return _AgentFeedCard(agent: item.data as Agent);
          } else {
            return _StoryFeedCard(story: item);
          }
        },
      ),
    );
    // TODO: 加载动画需要更优雅地集成
  }
}

// 用于实现吸顶效果的 Delegate
class _SliverFilterTagBarDelegate extends SliverPersistentHeaderDelegate {
  final _FilterTagBar child;

  _SliverFilterTagBarDelegate({required this.child});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppColors.background, // 确保背景色一致
      child: child,
    );
  }

  @override
  double get maxExtent => 50.0; // 高度与 _FilterTagBar 一致

  @override
  double get minExtent => 50.0; // 高度与 _FilterTagBar 一致

  @override
  bool shouldRebuild(_SliverFilterTagBarDelegate oldDelegate) {
    return child != oldDelegate.child;
  }
}

// Cards remain the same
class _AgentFeedCard extends StatelessWidget {
  final Agent agent;
  const _AgentFeedCard({required this.agent});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final authState = context.read<AuthBloc>().state;
        String? userId;
        if (authState is Authenticated) {
          userId = authState.userId;
        }
        if (userId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please log in to start a chat.')),
          );
          return;
        }
        final chatId = await context.read<ChatRepository>().startAgentChat(agent.id, userId);
        if (chatId != null && context.mounted) {
          context.push('/chat/$chatId');
        } else {
           if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to start chat. Please try again.')),
            );
          }
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          color: AppColors.secondaryBg,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AspectRatio(
                aspectRatio: 1.0,
                child: agent.imageUrl != null && agent.imageUrl!.isNotEmpty
                    ? Image.network(
                        agent.imageUrl!,
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                        errorBuilder: (_, __, ___) => Container(
                          color: AppColors.inputBackground,
                          child: const Icon(Icons.person, size: 64, color: AppColors.secondaryText),
                        ),
                      )
                    : Container(
                        color: AppColors.inputBackground,
                        child: const Icon(Icons.person, size: 64, color: AppColors.secondaryText),
                      ),
              ),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                      Text(agent.name, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14), maxLines: 1),
                      const SizedBox(height: 4),
                      Text(agent.description, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12), maxLines: 2, overflow: TextOverflow.ellipsis,),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 8,
                                backgroundImage: agent.avatarUrl != null && agent.avatarUrl!.isNotEmpty
                                  ? NetworkImage(agent.avatarUrl!)
                                  : null,
                                backgroundColor: Colors.grey,
                                child: agent.avatarUrl == null || agent.avatarUrl!.isEmpty
                                  ? const Icon(Icons.person, size: 10, color: Colors.white)
                                  : null,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                agent.author,
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: AppColors.secondaryText,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              const Icon(Icons.visibility, size: 12, color: AppColors.secondaryText),
                              const SizedBox(width: 2),
                              Text(
                                _formatCount(agent.viewCount),
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: AppColors.secondaryText,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StoryFeedCard extends StatelessWidget {
  final RecommendationItem story;
  const _StoryFeedCard({required this.story});
  @override
  Widget build(BuildContext context) {
    final storyData = story.data as InteractiveStoryCard;
    return GestureDetector(
      onTap: () async {
        final authState = context.read<AuthBloc>().state;
        String? userId;
        if (authState is Authenticated) {
          userId = authState.userId;
        }
        if (userId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please log in to start a story.')),
          );
          return;
        }
        final storyId = storyData.id;
        final chatId = await context.read<ChatRepository>().startStory(storyId, userId);
        if (chatId != null && context.mounted) {
          context.push('/chat/$chatId');
        } else {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to start story. Please try again.')),
            );
          }
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          color: AppColors.secondaryBg,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AspectRatio(
                aspectRatio: 1.0,
                child: storyData.coverUrl != null && storyData.coverUrl!.isNotEmpty
                    ? Image.network(
                        storyData.coverUrl!,
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                        errorBuilder: (_, __, ___) => Container(
                          color: AppColors.inputBackground,
                          child: const Icon(Icons.menu_book, size: 64, color: AppColors.secondaryText),
                        ),
                      )
                    : Container(
                        color: AppColors.inputBackground,
                        child: const Icon(Icons.menu_book, size: 64, color: AppColors.secondaryText),
                      ),
              ),
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(storyData.title, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14), maxLines: 2, overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 4),
                    Text(storyData.description ?? '暂无描述', style: const TextStyle(color: AppColors.secondaryText, fontSize: 12), maxLines: 2, overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const CircleAvatar(
                              radius: 8,
                              backgroundColor: Colors.grey,
                              child: Icon(Icons.person, size: 10, color: Colors.white),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              storyData.author,
                              style: const TextStyle(
                                fontSize: 10,
                                color: AppColors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.visibility, size: 12, color: AppColors.secondaryText),
                            const SizedBox(width: 2),
                            Text(
                              _formatCount(storyData.viewCount),
                              style: const TextStyle(
                                fontSize: 10,
                                color: AppColors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

String _formatCount(int count) {
  if (count >= 10000) {
    return '${(count / 10000).toStringAsFixed(1)}万';
  }
  return count.toString();
}
