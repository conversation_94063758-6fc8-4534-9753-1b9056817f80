#!/usr/bin/env python3
"""
测试流式输出修复的脚本
验证后端是否正确分离了reply和choices的生成
"""

import asyncio
import websockets
import json
import time

async def test_streaming_fix():
    """测试新的流式输出逻辑"""

    # 首先创建一个聊天会话
    import aiohttp
    import uuid

    # 生成有效的UUID格式的用户ID
    test_user_id = str(uuid.uuid4())

    # 简化测试：直接使用一个有效的UUID格式的chat_id进行测试
    # 这样可以专注于测试流式输出功能，而不是聊天会话创建
    chat_id = str(uuid.uuid4())
    print(f"🔧 使用测试chat_id: {chat_id}")

    # 为了测试，我们需要先在数据库中创建这个会话
    # 但现在我们先跳过这一步，直接测试WebSocket连接和消息处理

    # 连接到WebSocket
    uri = f"ws://localhost:8000/ws/chat/{chat_id}?user_id={test_user_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送测试消息
            test_message = {
                "content": "你好，请简单介绍一下自己",
                "action": "chat"
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"📤 发送消息: {test_message['content']}")
            
            # 记录时间戳
            start_time = time.time()
            first_chunk_time = None
            stream_end_time = None
            choices_time = None
            
            chunk_count = 0
            total_content = ""
            
            # 监听响应
            async for message in websocket:
                try:
                    data = json.loads(message)
                    current_time = time.time()
                    
                    if data.get('type') == 'message_chunk':
                        chunk_count += 1
                        chunk_content = data.get('content_chunk', '')
                        total_content += chunk_content
                        
                        if first_chunk_time is None:
                            first_chunk_time = current_time
                            delay = (first_chunk_time - start_time) * 1000
                            print(f"⚡ 首个chunk延迟: {delay:.2f}ms")
                        
                        # 显示chunk内容（截断长内容）
                        display_content = chunk_content.replace('\n', '\\n')
                        if len(display_content) > 30:
                            display_content = display_content[:30] + "..."
                        
                        elapsed = (current_time - start_time) * 1000
                        print(f"📦 Chunk {chunk_count:2d} ({elapsed:6.1f}ms): '{display_content}'")
                    
                    elif data.get('type') == 'stream_end':
                        stream_end_time = current_time
                        delay = (stream_end_time - start_time) * 1000
                        print(f"🏁 流式结束 ({delay:.2f}ms): 文本传输完成")
                        print(f"📊 总共接收 {chunk_count} 个chunks")
                        print(f"📝 完整内容长度: {len(total_content)} 字符")
                    
                    elif data.get('type') == 'choices_updated':
                        choices_time = current_time
                        delay = (choices_time - start_time) * 1000
                        choices = data.get('choices', [])
                        print(f"💡 选项生成完成 ({delay:.2f}ms): 收到 {len(choices)} 个选项")
                        
                        # 显示选项内容
                        for i, choice in enumerate(choices):
                            if isinstance(choice, dict):
                                choice_text = choice.get('text', str(choice))
                            else:
                                choice_text = str(choice)
                            print(f"   选项 {i+1}: {choice_text}")
                        
                        # 测试完成，退出
                        break
                    
                    elif data.get('type') == 'choices_failed':
                        print(f"❌ 选项生成失败: {data.get('error')}")
                        break
                    
                    elif data.get('type') == 'error':
                        print(f"❌ 服务器错误: {data.get('content')}")
                        break
                
                except json.JSONDecodeError:
                    print(f"⚠️  无法解析消息: {message}")
                except Exception as e:
                    print(f"⚠️  处理消息时出错: {e}")
            
            # 计算性能指标
            if first_chunk_time and stream_end_time and choices_time:
                total_time = (choices_time - start_time) * 1000
                stream_time = (stream_end_time - start_time) * 1000
                choices_gen_time = (choices_time - stream_end_time) * 1000
                
                print("\n📈 性能分析:")
                print(f"   首chunk延迟: {(first_chunk_time - start_time) * 1000:.2f}ms")
                print(f"   流式传输时间: {stream_time:.2f}ms")
                print(f"   选项生成时间: {choices_gen_time:.2f}ms")
                print(f"   总耗时: {total_time:.2f}ms")
                print(f"   流式传输占比: {(stream_time/total_time)*100:.1f}%")
                
                if stream_time < choices_gen_time:
                    print("✅ 成功！文本流式传输比选项生成更快，实现了真正的流式输出")
                else:
                    print("⚠️  警告：选项生成比文本传输更快，可能仍有阻塞")
    
    except Exception as e:
        print(f"❌ 连接失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试流式输出修复...")
    asyncio.run(test_streaming_fix())
