part of 'chat_player_bloc.dart';

abstract class ChatPlayerEvent extends Equatable {
  const ChatPlayerEvent();

  @override
  List<Object?> get props => [];
}

class ConnectToChat extends ChatPlayerEvent {
  final String chatId;
  final String userId;

  const ConnectToChat({required this.chatId, required this.userId});

  @override
  List<Object> get props => [chatId, userId];
}

// SendMessage 事件，携带目标ID
class SendMessage extends ChatPlayerEvent {
  final String content;
  final String? targetAgentId;

  const SendMessage({
    required this.content,
    this.targetAgentId,
  });

  @override
  List<Object?> get props => [content, targetAgentId];
}

class _MessageReceived extends ChatPlayerEvent {
  final dynamic message;

  const _MessageReceived(this.message);

  @override
  List<Object> get props => [message];
}

class _ConnectionClosed extends ChatPlayerEvent {}

class _GameStateSyncReceived extends ChatPlayerEvent {
  final List<Message> messages;
  final List<Agent> participants;
  final Agent? protagonistAgent;
  final bool hasHistoryMessages;

  const _GameStateSyncReceived({
    required this.messages,
    required this.participants,
    this.protagonistAgent,
    required this.hasHistoryMessages,
  });

  @override
  List<Object?> get props => [messages, participants, protagonistAgent, hasHistoryMessages];
}

// +++ 新增事件 +++
class SwitchTargetAgent extends ChatPlayerEvent {
  final Agent? agent; // agent为null时表示“自言自语”

  const SwitchTargetAgent(this.agent);

  @override
  List<Object?> get props => [agent];
}

// +++ 章节介绍和渐进式故事显示事件 +++
class StartChapterIntro extends ChatPlayerEvent {
  final List<Map<String, dynamic>> chapterElements;

  const StartChapterIntro(this.chapterElements);

  @override
  List<Object> get props => [chapterElements];
}

class DismissChapterIntro extends ChatPlayerEvent {}

class ShowNextStoryElement extends ChatPlayerEvent {}

class CompleteProgressiveStory extends ChatPlayerEvent {}

// --- vvv 整合自 StoryPlayerEvent 的事件 vvv ---

// 用户点击"点击继续" (在故事模式下)
class TapToContinue extends ChatPlayerEvent {}

// 用户做出了一个选择 (在故事模式下)
class MakeChoice extends ChatPlayerEvent {
  final String choiceText;
  const MakeChoice(this.choiceText);
  @override
  List<Object> get props => [choiceText];
}

// PRD要求：章节完成后继续游戏
class ContinueAfterChapter extends ChatPlayerEvent {}

// 继续当前章节（从章节完成状态返回到游戏状态）
class ContinueCurrentChapter extends ChatPlayerEvent {}

// 加载下一章节
class LoadNextChapter extends ChatPlayerEvent {}

// 下一章节加载成功事件（内部事件）
class _NextChapterLoaded extends ChatPlayerEvent {
  final String newChatId;

  const _NextChapterLoaded(this.newChatId);

  @override
  List<Object> get props => [newChatId];
}

// (内部事件) 接收到来自服务器的新剧情节点
class _NodeReceived extends ChatPlayerEvent {
  final story_models.StoryNode node;
  const _NodeReceived(this.node);
  @override
  List<Object> get props => [node];
}

// PRD要求：新的内部事件
// 暂时注释掉未使用的事件类，如果后续需要可以重新启用
// class _GameStateSynced extends ChatPlayerEvent {
//   final Map<String, dynamic> gameStateData;
//   const _GameStateSynced(this.gameStateData);
//   @override
//   List<Object> get props => [gameStateData];
// }

// class _ScoreReceived extends ChatPlayerEvent {
//   final Map<String, dynamic> scoreData;
//   const _ScoreReceived(this.scoreData);
//   @override
//   List<Object> get props => [scoreData];
// }

// PRD要求：实时评分反馈事件
class _ScoreUpdated extends ChatPlayerEvent {
  final int increment;
  final int newTotal;
  final bool isChapterComplete;

  const _ScoreUpdated({
    required this.increment,
    required this.newTotal,
    required this.isChapterComplete,
  });

  @override
  List<Object?> get props => [increment, newTotal, isChapterComplete];
}

// (内部事件) 接收到加载中状态
class _LoadingReceived extends ChatPlayerEvent {}

// (内部事件) 章节完成
class _ChapterCompleted extends ChatPlayerEvent {}

// (内部事件) 进入聊天场景
class _EnterChatScene extends ChatPlayerEvent {
  final List<Agent> agents;
  const _EnterChatScene(this.agents);
  @override
  List<Object> get props => [agents];
}

// 织梦者引擎：游戏状态变化事件
class _GameStateChanged extends ChatPlayerEvent {
  final List<String> descriptions;
  const _GameStateChanged(this.descriptions);
  @override
  List<Object> get props => [descriptions];
}

// 加载更多历史消息事件
class LoadMoreMessages extends ChatPlayerEvent {
  const LoadMoreMessages();
}

/// 用户请求重新生成选项
class RegenerateChoicesRequested extends ChatPlayerEvent {}

/// 用户请求展开/收缩选项框
class ToggleChoicesExpansion extends ChatPlayerEvent {}

/// 用户请求显示或隐藏建议选项面板
class ToggleChoicesPanelVisibility extends ChatPlayerEvent {}
