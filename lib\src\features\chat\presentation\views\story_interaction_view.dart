import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/features/chat/models/message_model.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import '../widgets/chat_input_bar.dart';
import '../widgets/message_list.dart';
import '../widgets/score_animation.dart';
import '../widgets/choice_buttons.dart';
import '../widgets/choice_loading_indicator.dart';
import '../widgets/chapter_completion_overlay.dart';

class StoryInteractionView extends StatefulWidget {
  const StoryInteractionView({super.key});

  @override
  State<StoryInteractionView> createState() => _StoryInteractionViewState();
}

class _StoryInteractionViewState extends State<StoryInteractionView> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isIntroDialogShown = false; // 防止重复弹窗的标志
  int _previousProgress = 0; // PRD要求：记录上一次的进度值用于计算增量

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 使用 didChangeDependencies 可以在 BLoC 状态更新后安全地显示对话框
    _showIntroDialogIfNeeded();
  }

  @override
  void dispose() {
    _isIntroDialogShown = false; // 重置标志，以防页面重新进入
    super.dispose();
  }

  void _showIntroDialogIfNeeded() {
    // 延迟一帧执行，确保 build 完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _isIntroDialogShown) return;

      final state = context.read<ChatPlayerBloc>().state;
      if (state.showChapterIntro && state.chapterElements != null) {
        setState(() {
          _isIntroDialogShown = true; // 标记为已显示
        });
        _showChapterIntroDialog(context, state);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ChatPlayerBloc, ChatPlayerState>(
      // PRD要求：监听分数变化、章节完成状态、选项变化和导航需求
      listenWhen: (previous, current) =>
        previous.currentProgress < current.currentProgress ||
        (previous.storyInteractionState != StoryInteractionState.finished &&
         current.storyInteractionState == StoryInteractionState.finished) ||
        (current.errorMessage != null && current.errorMessage!.startsWith('NAVIGATE_TO_CHAT:')),
      listener: (context, state) {
        // PRD要求：计算分数增量并触发动画
        final increment = state.currentProgress - _previousProgress;
        if (increment > 0) {
          if (context.mounted) { // 检查组件是否仍然挂载
            ScoreAnimation.show(context, increment);
          }
        }
        _previousProgress = state.currentProgress;

        // PRD要求：章节完成反馈 - 不再显示弹窗，改为覆盖层
        // 章节完成状态的UI现在通过Stack中的覆盖层处理

        // 新增：处理下一章导航
        if (state.errorMessage != null && state.errorMessage!.startsWith('NAVIGATE_TO_CHAT:')) {
          final newChatId = state.errorMessage!.substring('NAVIGATE_TO_CHAT:'.length);
          if (context.mounted) {
            // 使用GoRouter进行导航
            context.go('/chat/$newChatId');
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.black,
          endDrawer: _StoryDrawer(state: state),
          endDrawerEnableOpenDragGesture: false,
          body: Stack(
            children: [
              _buildStoryBackground(state),
              Column(
                children: [
                  _StoryTopHud(scaffoldKey: _scaffoldKey, state: state),
                  Expanded(child: MessageList(messages: state.messages, participants: state.participants, isStoryMode: true, protagonistAgent: state.protagonistAgent)),
                ],
              ),
              _StoryBottomHud(state: state),
              // PRD要求：提示系统UI - 悬浮按钮
              _HintsSystemUI(state: state),
              // --- ▼▼▼ 新增浮动精灵 ▼▼▼ ---
              const Positioned(
                bottom: 120, // 初始位置，可以根据输入框高度动态调整
                right: 20,
                child: _FloatingSprite(),
              ),
              // --- ▲▲▲ 新增结束 ▲▲▲ ---
              // 章节完成覆盖层
              if (state.storyInteractionState == StoryInteractionState.finished)
                ChapterCompletionOverlay(state: state),
            ],
          ),
        );
      },
    );
  }

  // 已移除旧的章节完成弹窗方法，现在使用ChapterCompletionOverlay覆盖层

  void _showChapterIntroDialog(BuildContext context, ChatPlayerState state) {
    print('=== _showChapterIntroDialog called ===');
    print('chapterElements: ${state.chapterElements}');

    if (state.chapterElements == null || state.chapterElements!.isEmpty) {
      print('No chapter elements, returning');
      return;
    }

    // 获取章节介绍内容
    final chapterElement = state.chapterElements!.first;
    final content = chapterElement['content_or_prompt'] as String? ?? '准备开始你的冒险...';
    final storyTitle = state.storyDetail?.title ?? '互动故事';
    final chapterTitle = state.storyDetail?.chapters.first.title ?? '第一章';

    print('Showing dialog with content: $content');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: AlertDialog(
            backgroundColor: AppColors.secondaryBg.withOpacity(0.95),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: const BorderSide(color: AppColors.accentPurple, width: 1),
            ),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  storyTitle,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  chapterTitle,
                  style: TextStyle(
                    color: AppColors.accentPurple,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Text(
                content,
                style: const TextStyle(
                  color: AppColors.primaryText,
                  fontSize: 15,
                  height: 1.6,
                ),
              ),
            ),
            actions: <Widget>[
              Container(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    // 触发DismissChapterIntro事件
                    context.read<ChatPlayerBloc>().add(DismissChapterIntro());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.accentPurple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    '开始探索',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

Widget _buildStoryBackground(ChatPlayerState state) {
  // 优先使用当前对话NPC的形象图，如果没有，则使用故事封面作为后备
  final imageUrl = state.currentTargetAgent?.imageUrl ?? state.storyDetail?.coverUrl;

  return Positioned.fill(
    child: AnimatedSwitcher(
      duration: const Duration(milliseconds: 800), // 过渡动画时长
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(opacity: animation, child: child); // 使用淡入淡出效果
      },
      child: Container(
        // 关键：使用 imageUrl 作为 Key，当它改变时，AnimatedSwitcher会触发动画
        key: ValueKey<String?>(imageUrl),
        decoration: BoxDecoration(
          image: (imageUrl != null && imageUrl.isNotEmpty)
              ? DecorationImage(
                  image: NetworkImage(imageUrl),
                  fit: BoxFit.cover,
                  onError: (_, __) {}, // 错误处理
                )
              : null,
          // 如果完全没有图片，显示默认渐变色
          gradient: (imageUrl == null || imageUrl.isEmpty)
              ? const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF2A1B3D), Color(0xFF44318D)],
                )
              : null,
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.black.withOpacity(0.3), Colors.black.withOpacity(0.85)],
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
}

class _StoryTopHud extends StatefulWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;
  final ChatPlayerState state;
  const _StoryTopHud({required this.scaffoldKey, required this.state});
  
  @override
  State<_StoryTopHud> createState() => _StoryTopHudState();
}

class _StoryTopHudState extends State<_StoryTopHud> {
  bool _isMissionExpanded = false;

  String _getMissionText(ChatPlayerState state) {
    final chapters = widget.state.storyDetail?.chapters;
    if (chapters != null && chapters.isNotEmpty) {
      final currentChapter = chapters.first;
      return currentChapter.missionText ?? currentChapter.title;
    }
    return '探索未知';
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                  onPressed: () => context.pop(),
                  style: ButtonStyle(backgroundColor: MaterialStateProperty.all(Colors.black.withOpacity(0.5))),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(color: Colors.black.withOpacity(0.5), borderRadius: BorderRadius.circular(15)),
                                      child: Text(
                    (widget.state.storyDetail?.chapters.isNotEmpty == true) 
                      ? widget.state.storyDetail!.chapters.first.title
                      : '第一章',
                    style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.menu, color: Colors.white),
                  onPressed: () => widget.scaffoldKey.currentState?.openEndDrawer(),
                  style: ButtonStyle(backgroundColor: MaterialStateProperty.all(Colors.black.withOpacity(0.5))),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // PRD要求：任务状态栏 - 显示任务目标和进度，点击可展开
            GestureDetector(
              onTap: () {
                setState(() {
                  _isMissionExpanded = !_isMissionExpanded;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.accentPurple.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    // 任务目标文本
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            "任务：${_getMissionText(widget.state)}",
                            style: const TextStyle(
                              color: AppColors.accentYellow, 
                              fontWeight: FontWeight.bold, 
                              fontSize: 12
                            ),
                            maxLines: _isMissionExpanded ? null : 1,
                            overflow: _isMissionExpanded ? null : TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          _isMissionExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                          color: Colors.white70,
                          size: 16,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // 进度条
                    Row(
                      children: [
                        Text(
                          '当前分值：${widget.state.currentProgress}/${widget.state.maxProgress}',
                          style: const TextStyle(color: Colors.white70, fontSize: 10),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: widget.state.currentProgress / widget.state.maxProgress,
                              backgroundColor: Colors.white.withOpacity(0.3),
                              valueColor: AlwaysStoppedAnimation<Color>(
                                widget.state.currentProgress >= widget.state.maxProgress 
                                  ? AppColors.accentGreen 
                                  : AppColors.accentPurple
                              ),
                              minHeight: 6,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _StoryBottomHud extends StatelessWidget {
  final ChatPlayerState state;
  const _StoryBottomHud({required this.state});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _buildBottomInteractiveLayer(context, state),
        ),
      ),
    );
  }

  Widget _buildBottomInteractiveLayer(BuildContext context, ChatPlayerState state) {
    // PRD要求：根据StoryInteractionState渲染不同的UI
    print('=== _buildBottomInteractiveLayer Debug ===');
    print('Current storyInteractionState: ${state.storyInteractionState}');
    print('isProgressiveStoryMode: ${state.isProgressiveStoryMode}');
    
    switch (state.storyInteractionState) {
      case StoryInteractionState.cinematic:
        print('Rendering cinematic continue button');
        return _buildCinematicContinueButton(context);
      case StoryInteractionState.gameplay:
        print('Rendering gameplay HUD with input');
        return _buildGameplayHud(context, state);
      case StoryInteractionState.finished:
        print('Rendering finished controls');
        return _buildChapterFinishedControls(context);
    }
  }

  Widget _buildCinematicContinueButton(BuildContext context) {
    return BlocBuilder<ChatPlayerBloc, ChatPlayerState>(
      builder: (context, state) {
        final isWaiting = state.isWaitingForNextMessage;

        return Container(
          key: const ValueKey('cinematic_button'), // Key for AnimatedSwitcher
          height: MediaQuery.of(context).size.height * 0.25, // 占据屏幕下方1/4区域
          child: GestureDetector(
            onTap: isWaiting ? null : () => context.read<ChatPlayerBloc>().add(TapToContinue()),
            child: Container(
              color: Colors.transparent, // 透明背景，PRD要求：全屏可点击
              child: Center(
                child: _CinematicContinueIndicator(isWaiting: isWaiting), // 传递等待状态
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameplayHud(BuildContext context, ChatPlayerState state) {
    return Container(
      key: const ValueKey('gameplay_hud'), // Key for AnimatedSwitcher
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.transparent, Colors.black.withOpacity(0.6), Colors.black.withOpacity(0.9)],
          stops: const [0.0, 0.3, 1.0],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 核心修改：使用 showChoicesPanel 控制可见性
            if (state.showChoicesPanel && state.isGeneratingChoices)
              const ChoiceLoadingIndicator(),
            if (state.showChoicesPanel && (state.currentChoices?.isNotEmpty ?? false))
              const ChoiceButtons(),
            ChatInputBar(state: state),
          ],
        ),
      ),
    );
  }



  Widget _buildChapterFinishedControls(BuildContext context) {
    return Container(
      key: const ValueKey('finished_controls'), // Key for AnimatedSwitcher
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                // 切换到gameplay状态，继续聊天
                // TODO: 需要添加专门的事件
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[800],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text('继续聊天'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                // 进入下一章的逻辑
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text('下一章'),
            ),
          ),
        ],
      ),
    );
  }
}

class _StoryDrawer extends StatefulWidget {
  final ChatPlayerState state;
  const _StoryDrawer({required this.state});

  @override
  State<_StoryDrawer> createState() => _StoryDrawerState();
}

class _StoryDrawerState extends State<_StoryDrawer> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isDescriptionExpanded = false;
  final Map<String, bool> _expandedChapters = {};
  final Map<String, bool> _expandedCharacters = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final storyDetail = widget.state.storyDetail;
    if (storyDetail == null) return const SizedBox.shrink();

    return Drawer(
      backgroundColor: const Color(0xFF1A1A2E),
      child: SafeArea(
        child: Column(
          children: [
            // 头部设置按钮
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.settings, color: Colors.white),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
            
            // 故事信息部分
            _buildStoryInfo(storyDetail),
            
            // 收藏和分享按钮
            _buildActionButtons(),
            
            // 标签栏
            _buildTabBar(),
            
            // 标签内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildChaptersTab(storyDetail),
                  _buildCharactersTab(storyDetail),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryInfo(storyDetail) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // 故事封面和基本信息
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 封面图
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: storyDetail.coverUrl != null
                      ? DecorationImage(
                          image: NetworkImage(storyDetail.coverUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                  color: storyDetail.coverUrl == null ? Colors.grey[800] : null,
                ),
                child: storyDetail.coverUrl == null
                    ? const Icon(Icons.book, color: Colors.white54)
                    : null,
              ),
              const SizedBox(width: 12),
              
              // 故事信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      storyDetail.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '墨夏  2024-11-08', // 静态数据，可以后续从API获取
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.chat_bubble_outline, 
                             color: Colors.white.withOpacity(0.7), size: 14),
                        const SizedBox(width: 4),
                        Text(
                          '60.9万',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(Icons.favorite_border, 
                             color: Colors.white.withOpacity(0.7), size: 14),
                        const SizedBox(width: 4),
                        Text(
                          '939',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 故事描述
          GestureDetector(
            onTap: () {
              setState(() {
                _isDescriptionExpanded = !_isDescriptionExpanded;
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  storyDetail.description ?? storyDetail.fullDescription,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: _isDescriptionExpanded ? null : 3,
                  overflow: _isDescriptionExpanded ? null : TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Icon(
                      _isDescriptionExpanded 
                          ? Icons.keyboard_arrow_up 
                          : Icons.keyboard_arrow_down,
                      color: Colors.white.withOpacity(0.7),
                      size: 16,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.favorite_border, size: 18),
              label: const Text('收藏'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.share, size: 18),
              label: const Text('分享'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.accentPurple,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.6),
        tabs: const [
          Tab(text: '章节'),
          Tab(text: '人物'),
        ],
      ),
    );
  }

  Widget _buildChaptersTab(storyDetail) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: storyDetail.chapters.length,
      itemBuilder: (context, index) {
        final chapter = storyDetail.chapters[index];
        final isExpanded = _expandedChapters[chapter.id] ?? false;
        
        // PRD要求：根据进度确定章节状态
        final isCompleted = _isChapterCompleted(chapter, index);
        final isCurrent = _isCurrentChapter(chapter, index);
        final isLocked = _isChapterLocked(chapter, index);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: isCompleted 
              ? AppColors.accentGreen.withOpacity(0.1)
              : isCurrent 
                ? AppColors.accentPurple.withOpacity(0.1)
                : Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCompleted 
                ? AppColors.accentGreen.withOpacity(0.3)
                : isCurrent 
                  ? AppColors.accentPurple.withOpacity(0.3)
                  : Colors.transparent,
            ),
          ),
          child: Column(
            children: [
              // 章节头部
              ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: isCompleted 
                      ? AppColors.accentGreen
                      : isCurrent 
                        ? AppColors.accentPurple
                        : Colors.grey[800],
                  ),
                  child: Icon(
                    isCompleted 
                      ? Icons.check_circle
                      : isCurrent 
                        ? Icons.play_circle_fill
                        : isLocked 
                          ? Icons.lock
                          : Icons.book,
                    color: isLocked ? Colors.grey[600] : Colors.white,
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        chapter.title ?? '章节 ${index + 1}',
                        style: TextStyle(
                          color: isLocked ? Colors.grey[600] : Colors.white,
                          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // 状态标签
                    if (isCompleted)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.accentGreen,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          '已完成',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ),
                    if (isCurrent && !isCompleted)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.accentPurple,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          '进行中',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ),
                    if (isLocked)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey[700],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '未解锁',
                          style: TextStyle(color: Colors.grey[400], fontSize: 10),
                        ),
                      ),
                  ],
                ),
                subtitle: Text(
                  chapter.description ?? chapter.missionText ?? '暂无描述',
                  style: TextStyle(
                    color: isLocked ? Colors.grey[700] : Colors.white70,
                    fontSize: 12,
                  ),
                  maxLines: isExpanded ? null : 1,
                  overflow: isExpanded ? null : TextOverflow.ellipsis,
                ),
                trailing: isLocked ? null : Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Colors.white54,
                ),
                onTap: isLocked ? null : () {
                  setState(() {
                    _expandedChapters[chapter.id] = !isExpanded;
                  });
                },
              ),
              
              // 展开内容
              if (isExpanded && !isLocked) ...[
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(color: Colors.white24),
                      const SizedBox(height: 8),
                      
                      // 章节详细信息
                      if (chapter.description != null) ...[
                        const Text(
                          '章节描述',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          chapter.description!,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 12,
                            height: 1.4,
                          ),
                        ),
                        const SizedBox(height: 12),
                      ],
                      
                      // 进度信息
                      if (isCurrent && widget.state.currentProgress > 0) ...[
                        const Text(
                          '当前进度',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: widget.state.currentProgress / widget.state.maxProgress,
                          backgroundColor: Colors.white.withOpacity(0.3),
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.accentPurple),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${widget.state.currentProgress}/${widget.state.maxProgress} 分',
                          style: const TextStyle(color: Colors.white70, fontSize: 11),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// PRD要求：判断章节是否已完成
  bool _isChapterCompleted(chapter, int index) {
    // 简单实现：除了当前章节外，之前的章节都算完成
    // 实际项目中应该基于taskProgress或其他状态数据判断
    return index == 0 && widget.state.currentProgress >= widget.state.maxProgress;
  }

  /// PRD要求：判断是否为当前章节
  bool _isCurrentChapter(chapter, int index) {
    // 简单实现：第一章为当前章节
    // 实际项目中应该基于当前游戏进度判断
    return index == 0;
  }

  /// PRD要求：判断章节是否被锁定
  bool _isChapterLocked(chapter, int index) {
    // 简单实现：除了第一章外，其他章节都锁定
    // 实际项目中应该基于解锁条件判断
    return index > 0;
  }

  Widget _buildCharactersTab(storyDetail) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: storyDetail.agents.length,
      itemBuilder: (context, index) {
        final agent = storyDetail.agents[index];
        final isExpanded = _expandedCharacters[agent.id] ?? false;
        
        // PRD要求：判断角色的互动状态
        final hasInteracted = _hasInteractedWithAgent(agent);
        final isMainCharacter = _isMainCharacter(agent, index);
        final friendshipLevel = _getFriendshipLevel(agent);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: isMainCharacter 
              ? AppColors.accentYellow.withOpacity(0.1)
              : Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isMainCharacter 
                ? AppColors.accentYellow.withOpacity(0.3)
                : Colors.transparent,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // 角色头部信息
              ListTile(
                contentPadding: const EdgeInsets.all(16),
                leading: Stack(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundImage: (agent.avatarUrl?.isNotEmpty ?? false) 
                        ? NetworkImage(agent.avatarUrl!) 
                        : null,
                      backgroundColor: AppColors.accentPurple.withOpacity(0.3),
                      child: (agent.avatarUrl?.isEmpty ?? true) 
                        ? Icon(Icons.person, color: Colors.white70, size: 30)
                        : null,
                    ),
                    // 互动状态指示器
                    if (hasInteracted)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.accentGreen,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.black, width: 2),
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 10,
                          ),
                        ),
                      ),
                    // 主角标识
                    if (isMainCharacter)
                      Positioned(
                        top: -5,
                        right: -5,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: AppColors.accentYellow,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.black, width: 2),
                          ),
                          child: const Icon(
                            Icons.star,
                            color: Colors.black,
                            size: 12,
                          ),
                        ),
                      ),
                  ],
                ),
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            agent.name,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: isMainCharacter ? FontWeight.bold : FontWeight.w600,
                            ),
                          ),
                        ),
                        // 角色标签
                        if (isMainCharacter)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.accentYellow,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '主角',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // 好感度显示
                    if (hasInteracted) ...[
                      Row(
                        children: [
                          Icon(
                            Icons.favorite,
                            color: _getFriendshipColor(friendshipLevel),
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getFriendshipText(friendshipLevel),
                            style: TextStyle(
                              color: _getFriendshipColor(friendshipLevel),
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: friendshipLevel / 100,
                              backgroundColor: Colors.white.withOpacity(0.2),
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getFriendshipColor(friendshipLevel),
                              ),
                              minHeight: 3,
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      Text(
                        '未互动',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ],
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    agent.description,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 13,
                      height: 1.3,
                    ),
                    maxLines: isExpanded ? null : 2,
                    overflow: isExpanded ? null : TextOverflow.ellipsis,
                  ),
                ),
                trailing: Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Colors.white70,
                ),
                onTap: () {
                  setState(() {
                    _expandedCharacters[agent.id] = !isExpanded;
                  });
                },
              ),
              
              // 展开的详细信息
              if (isExpanded) ...[
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(color: Colors.white24),
                      const SizedBox(height: 12),
                      
                      // 角色标签
                      if (agent.tags.isNotEmpty) ...[
                        const Text(
                          '角色标签',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 6,
                          runSpacing: 6,
                          children: agent.tags.map((tag) => Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppColors.accentPurple.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.accentPurple.withOpacity(0.5),
                              ),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                              ),
                            ),
                          )).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // 互动统计
                      if (hasInteracted) ...[
                        const Text(
                          '互动统计',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      '${_getInteractionCount(agent)}',
                                      style: const TextStyle(
                                        color: AppColors.accentPurple,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const Text(
                                      '对话次数',
                                      style: TextStyle(
                                        color: Colors.white70,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      '${friendshipLevel}%',
                                      style: TextStyle(
                                        color: _getFriendshipColor(friendshipLevel),
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const Text(
                                      '好感度',
                                      style: TextStyle(
                                        color: Colors.white70,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],
                      
                      // 互动按钮
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: hasInteracted ? () {
                            // TODO: 私聊逻辑
                          } : null,
                          icon: Icon(
                            hasInteracted ? Icons.chat : Icons.lock,
                            size: 16,
                          ),
                          label: Text(
                            hasInteracted ? '私聊' : '需要先在故事中互动',
                            style: const TextStyle(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: hasInteracted 
                              ? AppColors.accentPurple.withOpacity(0.8)
                              : Colors.grey[700],
                            foregroundColor: hasInteracted 
                              ? Colors.white 
                              : Colors.grey[400],
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// PRD要求：判断是否与角色有过互动
  bool _hasInteractedWithAgent(Agent agent) {
    // 检查消息历史中是否有该角色的消息
    return widget.state.messages.any((message) => 
      message.agentId == agent.id && message.role == MessageRole.assistant);
  }

  /// PRD要求：判断是否为主要角色
  bool _isMainCharacter(Agent agent, int index) {
    // 简单实现：第一个角色为主角，或者检查特定标签
    return index == 0 || agent.tags.contains('主角') || agent.tags.contains('主要角色');
  }

  /// PRD要求：获取与角色的好感度等级
  int _getFriendshipLevel(Agent agent) {
    // 简单实现：基于互动次数计算好感度
    final interactionCount = _getInteractionCount(agent);
    return (interactionCount * 10).clamp(0, 100);
  }

  /// PRD要求：获取与角色的互动次数
  int _getInteractionCount(Agent agent) {
    return widget.state.messages.where((message) => 
      message.agentId == agent.id && message.role == MessageRole.assistant).length;
  }

  /// PRD要求：根据好感度获取颜色
  Color _getFriendshipColor(int level) {
    if (level >= 80) return AppColors.accentGreen;
    if (level >= 60) return AppColors.accentYellow;
    if (level >= 40) return AppColors.accentOrange;
    if (level >= 20) return AppColors.accentPurple;
    return Colors.grey;
  }

  /// PRD要求：根据好感度获取文本
  String _getFriendshipText(int level) {
    if (level >= 80) return '挚友';
    if (level >= 60) return '好友';
    if (level >= 40) return '熟悉';
    if (level >= 20) return '认识';
    return '陌生';
  }
}

/// PRD要求：提示系统UI组件
class _HintsSystemUI extends StatelessWidget {
  final ChatPlayerState state;
  
  const _HintsSystemUI({required this.state});

  @override
  Widget build(BuildContext context) {
    // 暂时隐藏提示和灵感按钮
    return const SizedBox.shrink();

    // 原来的代码（已注释）
    // // 只在gameplay模式下显示提示按钮
    // if (state.storyInteractionState != StoryInteractionState.gameplay) {
    //   return const SizedBox.shrink();
    // }

    // return Positioned(
    //   right: 16,
    //   top: MediaQuery.of(context).size.height * 0.6, // 屏幕下半部分
    //   child: Column(
    //     children: [
    //       // 提示按钮
    //       _HintButton(
    //         icon: Icons.lightbulb_outline,
    //         label: '提示',
    //         count: 3, // 模拟用户拥有的提示卡数量
    //         color: AppColors.accentYellow,
    //         onTap: () => _showHintDialog(context, HintType.hint),
    //       ),
    //       const SizedBox(height: 12),
    //
    //       // 灵感按钮
    //       _HintButton(
    //         icon: Icons.psychology_outlined,
    //         label: '灵感',
    //         count: 2, // 模拟用户拥有的灵感卡数量
    //         color: AppColors.accentPurple,
    //         onTap: () => _showHintDialog(context, HintType.inspiration),
    //       ),
    //     ],
    //   ),
    // );
  }



  // TODO: 未来功能 - 提示卡系统已暂时移除以消除警告
  // 当提示系统UI被启用时，可以重新添加 _useHintCard 方法

  // TODO: 未来功能 - 商店系统已暂时移除以消除警告
  // 当商店功能被启用时，可以重新添加 _showStoreDialog 和 _simulateAdReward 方法
}



/// PRD要求：提示类型枚举
enum HintType {
  hint,       // 提示
  inspiration // 灵感
}

/// PRD要求：带呼吸动画的电影模式继续指示器
class _CinematicContinueIndicator extends StatefulWidget {
  final bool isWaiting; // 是否正在等待下一条消息

  const _CinematicContinueIndicator({this.isWaiting = false});

  @override
  State<_CinematicContinueIndicator> createState() => _CinematicContinueIndicatorState();
}

class _CinematicContinueIndicatorState extends State<_CinematicContinueIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    // PRD要求：2秒一次的呼吸动画
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // 缩放动画：1.0 -> 1.1 -> 1.0
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 透明度动画：0.7 -> 1.0 -> 0.7
    _opacityAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 根据等待状态决定是否启动动画
    if (!widget.isWaiting) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(_CinematicContinueIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当等待状态发生变化时，控制动画的启停
    if (widget.isWaiting != oldWidget.isWaiting) {
      if (widget.isWaiting) {
        _animationController.stop();
        _animationController.reset();
      } else {
        _animationController.repeat(reverse: true);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    widget.isWaiting ? Icons.hourglass_empty : Icons.touch_app_outlined,
                    color: widget.isWaiting ? Colors.white38 : Colors.white70,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.isWaiting ? '加载中...' : '点击继续',
                    style: TextStyle(
                      color: widget.isWaiting ? Colors.white38 : Colors.white70,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// 在文件底部添加新组件
class _FloatingSprite extends StatefulWidget {
  const _FloatingSprite();

  @override
  State<_FloatingSprite> createState() => _FloatingSpriteState();
}

class _FloatingSpriteState extends State<_FloatingSprite> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _animation = Tween<Offset>(
      begin: const Offset(0, -0.05),
      end: const Offset(0, 0.05),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 这里使用一个可爱的 Unicode 字符作为精灵，实际项目中可以替换为图片
    return SlideTransition(
      position: _animation,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withOpacity(0.1),
        ),
        child: const Text('👻', style: TextStyle(fontSize: 24)),
      ),
    );
  }
}