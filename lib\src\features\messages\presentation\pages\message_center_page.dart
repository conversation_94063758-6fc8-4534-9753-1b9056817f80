// 文件路径: lib/src/features/messages/presentation/pages/message_center_page.dart
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../bloc/message_center_bloc.dart';
import '../../../auth/bloc/auth_bloc.dart';
import '../../models/chat_list_item.dart';
import '../../repository/message_repository.dart';

class MessageCenterPage extends StatelessWidget {
  const MessageCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MessageCenterBloc(
        repository: RepositoryProvider.of<MessageRepository>(context),
        authBloc: BlocProvider.of<AuthBloc>(context),
      )..add(LoadChatList()),
      child: Scaffold(
        // --- ▼▼▼ 核心修改 1：添加 AppBar ▼▼▼ ---
        appBar: AppBar(
          backgroundColor: Colors.transparent, // AppBar 透明，以显示下层渐变
          elevation: 0,
          scrolledUnderElevation: 0,
          centerTitle: true,
          // --- ▼▼▼ 核心修改区域开始 ▼▼▼ ---
          // 使用自定义的辉光标题组件替换原有Text组件
          title: _buildGlowingTitle('聊', '天'),
          // --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.inputBackground.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.flash_on, color: AppColors.accentYellow, size: 16),
                      SizedBox(width: 4),
                      Text('免费能量', style: TextStyle(color: Colors.white, fontSize: 12)),
                    ],
                  ),
                ),
              ),
            ),
          ],
          // 使用 flexibleSpace 在 AppBar 底部绘制渐变，与 Body 无缝衔接
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryGradientStart,
                  AppColors.primaryGradientEnd,
                ],
              ),
            ),
          ),
        ),
        // --- ▲▲▲ 核心修改结束 ▲▲▲ ---
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primaryGradientStart,
                AppColors.primaryGradientEnd,
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: -100,
                right: -100,
                child: Container(
                  width: 300,
                  height: 300,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.white.withOpacity(0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // --- ▼▼▼ 核心修改 2：移除旧 Header，添加新 Header ▼▼▼ ---
              ListView(
                padding: const EdgeInsets.only(bottom: 100),
                children: [
                  _buildEventNotificationsHeader(), // 使用新的、只包含“事件通知”标题的 Header
                  _buildEventNotificationsSection(),
                  _buildFriendDiscoverySection(),
                  _buildMessagesListSection(),
                ],
              ),
              // --- ▲▲▲ 核心修改结束 ▲▲▲ ---
              _buildBottomBanner(),
            ],
          ),
        ),
      ),
    );
  }
}

// --- ▼▼▼ 核心修改区域：为辉光标题添加图标 ▼▼▼ ---
Widget _buildGlowingTitle(String firstChar, String secondChar) {
  return Row(
    mainAxisSize: MainAxisSize.min, // 保证Row只占用所需宽度，从而能在AppBar中居中
    crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐图标和文字
    children: [
      // 新增的 Sparkle 图标
      Icon(
        Icons.auto_awesome, // 闪耀图标
        color: Colors.white.withOpacity(0.9), // 半透明白色，符合UI风格
        size: 22,
        shadows: [
          // 为图标也加上辉光效果
          Shadow(
            color: Colors.white.withOpacity(0.5),
            blurRadius: 10.0,
          ),
        ],
      ),
      const SizedBox(width: 8), // 图标和文字的间距

      // 原有的错落文字效果
      Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            firstChar,
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 26, // 第一个字更大
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.6),
                  blurRadius: 15.0,
                ),
                Shadow(
                  color: Colors.white.withOpacity(0.3),
                  blurRadius: 25.0,
                ),
              ],
            ),
          ),
          const SizedBox(width: 4), // 字符间距
          Text(
            secondChar,
            style: TextStyle(
              fontFamily: 'BaiWuChangKeKeTi', // <--- 在这里应用字体
              fontSize: 20, // 第二个字较小
              fontWeight: FontWeight.bold,
              color: Colors.white,
               shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.5),
                  blurRadius: 10.0,
                ),
              ],
            ),
          ),
        ],
      ),
    ],
  );
}
// --- ▲▲▲ 核心修改区域结束 ▲▲▲ ---

// --- ▼▼▼ 核心修改 4：创建新的、更简洁的 Header 用于“事件通知”部分 ▼▼▼ ---
Widget _buildEventNotificationsHeader() {
  return Padding(
    padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text('事件通知', style: AppTextStyles.headline2.copyWith(fontSize: 16)),
            const SizedBox(width: 4),
            const Text('(3)', style: TextStyle(color: AppColors.secondaryText, fontSize: 16)),
          ],
        ),
        TextButton(
          onPressed: () {},
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('全部事件', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
              Icon(Icons.arrow_forward_ios, size: 12, color: AppColors.secondaryText),
            ],
          ),
        )
      ],
    ),
  );
}
// --- ▲▲▲ 核心修改结束 ▲▲▲ ---

Widget _buildEventNotificationsSection() {
  final notifications = [
    {'title': '容身之处', 'subtitle': '初次见面，继兄将我堵在门口……', 'icon': Icons.home_work_outlined, 'color': AppColors.accentBlue, 'tag': '新关系'},
    {'title': '抢亲', 'subtitle': '把你从花轿上抢过来，你肯定恨极了我吧。', 'icon': Icons.favorite_border, 'color': AppColors.accentPurple, 'tag': '新事件'},
    {'title': '私藏心事', 'subtitle': '一个眼神的交汇，足以让他所有的伪装瞬间瓦解。', 'icon': Icons.lock_outline, 'color': AppColors.accentYellow, 'tag': '新事件'},
  ];
  return Container(
    height: 105,
    margin: const EdgeInsets.only(left: 16, top: 8),
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final item = notifications[index];
        return _buildEventCard(
          title: item['title'] as String,
          subtitle: item['subtitle'] as String,
          icon: item['icon'] as IconData,
          color: item['color'] as Color,
          tag: item['tag'] as String,
        );
      },
    ),
  );
}

Widget _buildEventCard({required String title, required String subtitle, required IconData icon, required Color color, required String tag}) {
  return Container(
    width: 200,
    margin: const EdgeInsets.only(right: 12),
    // 使用 ClipRRect 为磨砂效果提供圆角
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      // 使用 BackdropFilter 实现磨砂玻璃效果
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15.0, sigmaY: 15.0), // 可以适当增加模糊度
        child: Container(
          decoration: BoxDecoration(
            // 核心修改：使用径向渐变实现“中间暗、四周亮”
            gradient: RadialGradient(
              center: Alignment.center, // 从中心开始渐变
              radius: 1.0, // 渐变半径覆盖整个卡片
              colors: [
                Colors.white.withOpacity(0.1), // 中心颜色：更透明，显得更暗
                Colors.white.withOpacity(0.2), // 边缘颜色：不透明度更高，显得更亮
              ],
              stops: const [0.0, 1.0], // 从中心到边缘平滑过渡
            ),
            borderRadius: BorderRadius.circular(16.0),
            // 核心修改：使用稍亮的边框模拟边缘高光
            border: Border.all(
              color: Colors.white.withOpacity(0.25),
              width: 1.0,
            ),
            // 核心修改：添加阴影以营造立体感
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 15.0,
                spreadRadius: 1.0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          // Stack 及其内部内容保持不变
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(color: color.withOpacity(0.2), borderRadius: BorderRadius.circular(8)),
                          child: Row(
                            children: [
                              Icon(icon, color: color, size: 12),
                              const SizedBox(width: 4),
                              Text(tag, style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(title, style: const TextStyle(color: AppColors.primaryText, fontWeight: FontWeight.bold, fontSize: 14)),
                    const SizedBox(height: 4),
                    Text(subtitle, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12), maxLines: 2, overflow: TextOverflow.ellipsis),
                  ],
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: InkWell(onTap: () {}, child: const Icon(Icons.close, color: AppColors.secondaryText, size: 16)),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget _buildFriendDiscoverySection() {
  final friendUpdates = [{'name': '谢疏白', 'avatar': 'https://i.imgur.com/example_avatar1.png', 'tag': '限免'}];
  return Padding(
    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text('好友消息', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
            const Spacer(),
            // 6. 新增“吃醋”和“冷落”状态
            Text('😡 吃醋 0 >', style: TextStyle(color: AppColors.secondaryText, fontSize: 12)),
            const SizedBox(width: 8),
            Text('🥶 冷落 0 >', style: TextStyle(color: AppColors.secondaryText, fontSize: 12)),
          ],
        ),
        const SizedBox(height: 12),
        // 2. 修正溢出错误
        SizedBox(
          height: 95, // 稍微增加高度以容纳文本
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: friendUpdates.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) return _buildAddFriendButton(context);
              final friend = friendUpdates[index - 1];
              return _buildFriendDiscoveryItem(friend);
            },
          ),
        ),
      ],
    ),
  );
}

Widget _buildAddFriendButton(BuildContext context) {
  return GestureDetector(
    onTap: () {
      // 跳转到推荐页面（导航栏索引3对应推荐页面）
      context.go('/recommend');
    },
    child: Container(
      width: 60,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(shape: BoxShape.circle, color: AppColors.secondaryBg, border: Border.all(color: AppColors.tertiaryText, width: 1)),
            child: const Icon(Icons.add, color: AppColors.primaryText, size: 28),
          ),
          const SizedBox(height: 8),
          const Text('发现好友', style: TextStyle(color: AppColors.primaryText, fontSize: 12)),
        ],
      ),
    ),
  );
}

Widget _buildFriendDiscoveryItem(Map<String, String> friend) {
  return Container(
    width: 60,
    margin: const EdgeInsets.only(right: 16),
    child: Column(
      children: [
        Stack(
          clipBehavior: Clip.none,
          children: [
            CircleAvatar(radius: 28, backgroundImage: NetworkImage(friend['avatar']!)),
            Positioned(
              top: -2,
              right: -4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(color: AppColors.accentPurple, borderRadius: BorderRadius.circular(8)),
                child: Text(friend['tag']!, style: const TextStyle(color: Colors.white, fontSize: 8, fontWeight: FontWeight.bold)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(friend['name']!, style: const TextStyle(color: AppColors.primaryText, fontSize: 12), overflow: TextOverflow.ellipsis),
      ],
    ),
  );
}

Widget _buildMessagesListSection() {
  return BlocBuilder<MessageCenterBloc, MessageCenterState>(
    builder: (context, state) {
      if (state is MessageCenterLoading) return const Center(child: CircularProgressIndicator(color: AppColors.accentPurple));
      if (state is MessageCenterLoaded) {
        if (state.chatList.isEmpty) return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: Text('暂无消息', style: TextStyle(color: AppColors.secondaryText))));
        
        return Column(
          children: state.chatList.map((chat) {
            return Column(
              children: [
                _buildChatListItem(context, chat),
                // 5. 使用 Padding 实现缩进分隔线
                Padding(
                  padding: const EdgeInsets.only(left: 80.0, right: 16.0),
                  child: Divider(color: AppColors.secondaryBg.withOpacity(0.5), height: 1),
                ),
              ],
            );
          }).toList(),
        );
      }
      if (state is MessageCenterError) return Center(child: Text(state.message, style: const TextStyle(color: Colors.red)));
      return const SizedBox.shrink();
    },
  );
}

Widget _buildChatListItem(BuildContext context, ChatListItem chat) {
  return InkWell(
    onTap: () {
      context.push('/chat/${chat.chatId}').then((_) {
        print("Returned from chat page, refreshing chat list...");
        context.read<MessageCenterBloc>().add(LoadChatList());
      });
    },
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start, // 垂直方向顶部对齐
        children: [
          // 头像 (不变)
          CircleAvatar(
            radius: 28,
            backgroundImage: (chat.displayAvatar != null && chat.displayAvatar!.isNotEmpty) ? NetworkImage(chat.displayAvatar!) : null,
            child: (chat.displayAvatar == null || chat.displayAvatar!.isEmpty) ? const Icon(Icons.person) : null,
          ),
          const SizedBox(width: 12),
          
          // 中间信息区域 (核心修改区域)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户名、等级、状态行 (不变)
                Row(
                  children: [
                    Text(chat.displayName, style: AppTextStyles.body.copyWith(fontSize: 16, fontWeight: FontWeight.w500)),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(color: AppColors.accentPurple.withOpacity(0.3), borderRadius: BorderRadius.circular(4)),
                      child: Row(
                        children: [
                          const Icon(Icons.favorite, color: AppColors.accentPurple, size: 10),
                          const SizedBox(width: 2),
                          Text('Lv.${chat.level}', style: const TextStyle(color: AppColors.accentPurple, fontSize: 10, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                    if (chat.relationshipStatus != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(color: Colors.pink.withOpacity(0.3), borderRadius: BorderRadius.circular(4)),
                        child: Text(chat.relationshipStatus!, style: const TextStyle(color: Colors.pinkAccent, fontSize: 10, fontWeight: FontWeight.bold)),
                      ),
                    ]
                  ],
                ),
                const SizedBox(height: 6),
                
                // ▼▼▼【核心布局修复】▼▼▼
                // 将消息预览和红点/标签放在同一个 Row 中
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 让 Text 组件弹性伸缩
                    Expanded(
                      child: Text(
                        chat.latestMessage ?? '...',
                        style: AppTextStyles.bodySecondary.copyWith(fontSize: 14),
                        maxLines: 1, // 强制单行显示
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    
                    // 将红点/标签放在这里
                    if (chat.unreadCount > 0) ...[
                      const SizedBox(width: 8), // 与文本的间距
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                        child: Text(
                          chat.unreadCount.toString(),
                          style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ] else if (chat.eventTag != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(color: AppColors.accentBlue.withOpacity(0.8), borderRadius: BorderRadius.circular(10)),
                        child: Text(
                          chat.eventTag!,
                          style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ]
                  ],
                ),
                // ▲▲▲【核心布局修复】▲▲▲
              ],
            ),
          ),
          
          // 右侧时间戳 (现在只负责显示时间)
          Padding(
            padding: const EdgeInsets.only(left: 8.0, top: 2.0), // 微调间距和对齐
            child: Text(
              _formatTimestamp(chat.latestMessageTime ?? DateTime.now().toIso8601String()),
              style: AppTextStyles.caption.copyWith(fontSize: 12),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildBottomBanner() {
  return Positioned(
    bottom: 83, // <-- 修改1: 定位在导航栏正上方，无缝衔接
    left: 0,    // <-- 修改2: 左右达到屏幕边缘
    right: 0,   // <-- 修改2: 左右达到屏幕边缘
    child: ClipRRect( 
      // 修改3: 只对顶部设置圆角
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20.0)),
      child: BackdropFilter( 
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15), // 增强模糊效果
        child: Container(
          // 修改4: 增加左右内边距，保证内容安全
          padding: const EdgeInsets.fromLTRB(24, 8, 16, 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.accentPurple.withOpacity(0.5),
                AppColors.accentBlue.withOpacity(0.3),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            // 修改5: 边框也只设置顶部
            border: Border(
              top: BorderSide(color: Colors.white.withOpacity(0.2)),
            ),
          ),
          child: Row(
            // Row 内部的内容保持不变
            children: [
              const Icon(Icons.cloud_outlined, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              const Expanded(child: Text('是否要接收TA的吃醋、冷落消息~', style: TextStyle(color: Colors.white, fontSize: 12))),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.circular(10)),
                child: const Text('100+', style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold)),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accentPurple,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  minimumSize: const Size(0, 32),
                ),
                child: const Text('接受', style: TextStyle(color: Colors.white, fontSize: 12)),
              ),
              IconButton(onPressed: () {}, icon: const Icon(Icons.close, color: Colors.white, size: 20)),
            ],
          ),
        ),
      ),
    ),
  );
}


String _formatTimestamp(String isoString) {
  try {
    // 将ISO字符串解析为本地时区的DateTime对象
    final time = DateTime.parse(isoString).toLocal();
    // 直接使用 'MM/dd HH:mm' 格式进行格式化
    return DateFormat('MM/dd HH:mm').format(time);
  } catch (e) {
    // 如果解析失败，返回原始字符串的月日部分作为备用
    return isoString.substring(5, 10);
  }
}