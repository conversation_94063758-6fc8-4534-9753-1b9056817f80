import 'dart:async';
import 'package:flutter/material.dart';
import 'package:xinglian/src/common/widgets/rich_typewriter_text.dart';
import 'package:xinglian/src/common/widgets/typewriter_text.dart';

/// 测试打字机组件优化效果的页面
class TypewriterOptimizationTest extends StatefulWidget {
  const TypewriterOptimizationTest({super.key});

  @override
  State<TypewriterOptimizationTest> createState() => _TypewriterOptimizationTestState();
}

class _TypewriterOptimizationTestState extends State<TypewriterOptimizationTest> {
  String _streamingText = "";
  Timer? _simulationTimer;
  int _currentIndex = 0;
  
  // 模拟流式文本数据
  final List<String> _textChunks = [
    "你好，",
    "我是",
    "一个",
    "AI助手。",
    "今天",
    "天气",
    "很好，",
    "适合",
    "出去",
    "散步。",
    "你有什么",
    "想要",
    "聊的",
    "话题吗？",
    "我可以",
    "和你",
    "讨论",
    "任何",
    "有趣的",
    "内容。"
  ];

  @override
  void dispose() {
    _simulationTimer?.cancel();
    super.dispose();
  }

  void _startStreamingSimulation() {
    _simulationTimer?.cancel();
    _streamingText = "";
    _currentIndex = 0;
    
    _simulationTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (_currentIndex < _textChunks.length) {
        setState(() {
          _streamingText += _textChunks[_currentIndex];
          _currentIndex++;
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _resetTest() {
    _simulationTimer?.cancel();
    setState(() {
      _streamingText = "";
      _currentIndex = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('打字机组件优化测试'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 测试说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🚀 优化效果测试',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '此测试模拟流式文本输入，验证打字机组件的优化效果：\n'
                    '• 增量更新时动画连续性\n'
                    '• 计时器管理优化\n'
                    '• 流畅的视觉反馈',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 控制按钮
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _startStreamingSimulation,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('开始流式测试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _resetTest,
                  icon: const Icon(Icons.refresh),
                  label: const Text('重置'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 普通打字机组件测试
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '普通打字机组件 (TypewriterText)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TypewriterText(
                    _streamingText,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                    ),
                    duration: const Duration(milliseconds: 50),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 富文本打字机组件测试
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '富文本打字机组件 (RichTypewriterText)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  RichTypewriterText(
                    _streamingText,
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                    ),
                    duration: const Duration(milliseconds: 50),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 状态信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📊 当前状态',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('文本长度: ${_streamingText.length} 字符'),
                  Text('已处理块: $_currentIndex / ${_textChunks.length}'),
                  Text('模拟状态: ${_simulationTimer?.isActive == true ? "运行中" : "已停止"}'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 在main.dart中添加此路由来测试
/// 
/// 使用方法:
/// 1. 在你的应用中导航到此页面
/// 2. 点击"开始流式测试"按钮
/// 3. 观察两个打字机组件的表现
/// 4. 验证文本增量更新时动画的连续性
/// 
/// 预期效果:
/// - 文本应该流畅地逐字符显示
/// - 新的文本块到达时，打字机不应该重新开始
/// - 动画应该从当前位置继续，而不是重置